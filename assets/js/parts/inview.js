$(document).ready(function(){
    $(document).on("initPage", function(){
      checkInviewClasses();
      splitLines();
      scroller.on("scroll", function(e) {
        checkInviewClasses();
      });
    });

});


function checkInviewClasses() {
    $("[data-init]").each(function() {
      const scrollDirect = $(this).data("scroll-direct");
      ScrollTrigger.create({
        trigger: this,
        start: scrollDirect ? "0% 100%" : "0% 90%",
        end: "0% 90%",
        onEnter: () => $(this).addClass("inview")
      });
    });
}

function splitLines(splitThis) {
  $("[data-split-text]").each(function(i, el) {
    var split = new SplitText(el, { type: "lines, words", linesClass: "line", wordsClass: "word" });
  });
}