// out: false

  @font-face {
    font-family: 'Bree Serif';
      src: url('assets/fonts/BreeSerif-Regular.woff2') format('woff2'),
           url('assets/fonts/BreeSerif-Regular.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: block;
  }


@font-face {
    font-family: 'icomoon';
    src:  url('assets/fonts/icomoon.eot?qwq3hi');
    src:  url('assets/fonts/icomoon.eot?qwq3hi#iefix') format('embedded-opentype'),
      url('assets/fonts/icomoon.ttf?qwq3hi') format('truetype'),
      url('assets/fonts/icomoon.woff?qwq3hi') format('woff'),
      url('assets/fonts/icomoon.svg?qwq3hi#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
  }
  
  [class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: never; 
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .icon-youtube:before {
    content: "\e900";
  }
  .icon-spotify:before {
    content: "\e901";
  }
  .icon-instagram:before {
    content: "\e902";
  }
  .icon-facebook:before {
    content: "\e903";
  }
  .icon-contact:before {
    content: "\e904";
  }
  .icon-agenda:before {
    content: "\e905";
  }
  .icon-newspaper:before {
    content: "\e906";
  } 
  .icon-file:before {
    content: "\e907";
  }
  .icon-download:before {
    content: "\e908";
  }
  .icon-arrow-left:before {
    content: "\e909";
  }
  .icon-arrow-right:before {
    content: "\e90a";
  }