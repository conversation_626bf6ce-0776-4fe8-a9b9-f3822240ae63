// out: false

section {
    &.inview {
        [data-lines] {
        visibility: visible;
        .line {
            .staggerWordsChild(100, 0.15s, 0.4s);
            .word, .letter {
            .transform(translateY(0));
            .transitionMore(transform, .75s, 0s, cubic-bezier(0, 0.55, 0.45, 1));
            }
        }
        }
    }
    [data-lines] {
        .line {
        position: relative;
        overflow: hidden;
        .word, .letter {
            .transform(translateY(100%));
            will-change: transform;
        }
        }
    }
}

[data-lines] {
  visibility: hidden;
}

.hugeTitle {
    font-family: '<PERSON><PERSON>ri'; 
    font-size: @vw100;
    font-weight: 800;
    text-transform: uppercase;
    line-height: .9;
    &.white {
        color: @hardWhite;
    }
    strong, em {
        color: @secondaryColor;
        font-style: normal;
    }
}

.normalTitle {
    font-family: 'Bree Serif'; 
    font-size: @vw56;
    font-weight: 600;
    line-height: 1.3;
    font-style: italic;
    &.white {
        color: @hardWhite;
    }
    strong, em {
        color: @secondaryColor;
        font-style: normal;
        font-weight: 600;
    }
}

.tinyTitle {
    font-family: 'Bree Serif'; 
    font-size: @vw18;
    color: @primaryColor;
    font-weight: 600;
    line-height: 1.1;
    &.white {
        color: @hardWhite;
    }
}

.quoteTitle {
    font-family: 'Bree Serif'; 
    font-style: italic;
    font-size: @vw40;
    color: @thirdColor;
    line-height: 1.4;
    .line {
        padding-right: @vw10;
    }
}

@media all and (max-width: 1160px) {
    [data-split-text] {
        .line {
            .transform(translateY(@vw10-1160) rotate(10deg) skewX(10deg) scale(.2));
        }
    }

    .hugeTitle {
        font-size: @vw100-1160;
    }

    .normalTitle {
        font-size: @vw60-1160;
    }

    .quoteTitle {
        font-size: @vw32-1160;
    }
}

@media all and (max-width: 580px) {
    [data-split-text] {
        .line {
            .transform(translateY(@vw10-580) rotate(10deg) skewX(10deg) scale(.2));
        }
    }

    .hugeTitle {
        font-size: @vw60-580;
    }

    .normalTitle {
        font-size: @vw60-580;
        line-height: .9;
    }

    .quoteTitle {
        font-size: @vw32-580;
    }
}