<?php
// Haal algemene informatie uit theme customizer
$content = get_field("content");
$site_title = get_theme_mod('customTheme-main-callout-title');
$contactpersoon = get_theme_mod('customTheme-main-callout-contactpersoon', '<PERSON><PERSON><PERSON>');
$adres = get_theme_mod('customTheme-main-callout-address');
$zipcode = get_theme_mod('customTheme-main-callout-zipcode');
$telefoonnummer = get_theme_mod('customTheme-main-callout-telephone');
$telephone_label = get_theme_mod('customTheme-main-callout-telephone-label');
$email = get_theme_mod('customTheme-main-callout-mail');
$facebook = get_theme_mod('customTheme-main-callout-facebook');
$linkedin = get_theme_mod('customTheme-main-callout-linkedin');
$instagram = get_theme_mod('customTheme-main-callout-instagram');
$title = get_field("title");
$quote = get_field("quote");
// Gebruik site title als bedrijfsnaam
$bedrijfsnaam = $site_title;
?>
<section class="algemeenTekstRiseBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="cols">
            <div class="col">
                <div class="intro">
                <?php if( $content ): ?>
                    <p class="biggerText" data-lines data-words>
                        <?php echo ($quote); ?>
                    </p>
                <?php endif; ?>
                </div>
                <div class="textCard">
                     <?php if( $title ): ?>
                        <h2 class="tinyTitle"><?php echo esc_html($title); ?></h2>
                        <?php endif; ?>

                        <?php if( $content ): ?>
                        <div class="introContent">
                            <?php echo wpautop($content); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col">
                <div class="algemeneInfoCard">
                    <h3 class="tinyTitle"><?php echo esc_html($site_title); ?></h3>
                    <div class="infoList">
                        <?php if( $contactpersoon ): ?>
                        <div class="infoItem">
                            <i><?php echo esc_html($contactpersoon); ?></i>
                        </div>
                        <?php endif; ?>

                        <?php if( $adres ): ?>
                        <div class="infoItem">
                            <?php echo nl2br(esc_html($adres)); ?>
                        </div>
                        <?php endif; ?>

                        <?php if( $zipcode ): ?>
                        <div class="infoItem">
                            <?php echo esc_html($zipcode); ?>
                        </div>
                        <?php endif; ?>

                        <?php if( $telefoonnummer ): ?>
                        <div class="infoItem">
                            <a href="tel:<?php echo esc_attr(str_replace(' ', '', $telefoonnummer)); ?>">
                                <?php echo esc_html($telephone_label); ?>
                            </a>
                        </div>
                        <?php endif; ?>

                        <?php if( $email ): ?>
                        <div class="infoItem">
                            <a class="mailIcon" title="Mail" href="mailto:<?php echo esc_attr($email); ?>">
                                <svg viewBox="0 0 23.59 17.63">
                                    <path d="M22.51.22l-10.39,10.37c-.18.11-.46.11-.64,0-.14-.08-.99-.93-1.2-1.13C7.13,6.47,4.18,3.25,1.08.22l.95-.22h19.6s.88.22.88.22Z"/>
                                    <path d="M22.37,17.48l-.81.14H1.96s-.74-.14-.74-.14l7.65-7.61c.86.73,1.66,2.1,2.89,2.12s2.09-1.38,2.96-2.12l7.65,7.61Z"/>
                                    <path d="M.24,16.5l-.14-.6c.22-4.62-.29-9.51,0-14.11.01-.18.05-.35.07-.53l7.75,7.65L.24,16.5Z"/>
                                    <path d="M23.36,16.49l-7.68-7.58,7.75-7.65c.03.18.06.34.07.53.29,4.6-.22,9.49,0,14.11l-.14.6Z"/>
                                </svg>
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
