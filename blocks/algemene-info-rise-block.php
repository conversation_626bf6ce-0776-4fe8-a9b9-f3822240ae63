<?php
// Haal intro velden op
$title = get_field('title');
$content = get_field('content');

// Haal algemene informatie uit theme customizer
$site_title = get_bloginfo('name');
$contactpersoon = get_theme_mod('customTheme-main-callout-contactpersoon', '<PERSON><PERSON><PERSON>');
$adres = get_theme_mod('customTheme-main-callout-address');
$zipcode = get_theme_mod('customTheme-main-callout-zipcode');
$telefoonnummer = get_theme_mod('customTheme-main-callout-telephone');
$telephone_label = get_theme_mod('customTheme-main-callout-telephone-label');
$email = get_theme_mod('customTheme-main-callout-mail');
$facebook = get_theme_mod('customTheme-main-callout-facebook');
$linkedin = get_theme_mod('customTheme-main-callout-linkedin');
$instagram = get_theme_mod('customTheme-main-callout-instagram');
?>
<section class="algemeneInfoRiseBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="innerContent">
            <?php if( $title ): ?>
            <h2 class="normalTitle"><?php echo esc_html($title); ?></h2>
            <?php endif; ?>

            <?php if( $content ): ?>
            <div class="introContent">
                <?php echo wpautop($content); ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>
