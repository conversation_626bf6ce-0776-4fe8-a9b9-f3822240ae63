<?php
$size = 'medium'; // (thumbnail, medium, large, full, or custom size)
$image = get_field("image");
$anchor = get_field("anchor");
$title = get_field("title");
$subtitle = get_field("subtitle");
$cta_title = get_field("cta_title");
$mail = get_theme_mod('customTheme-main-callout-mail');
?>
<section class="ctaBlock blue" data-init <?php echo $anchor ? 'data-anchor="' . esc_attr($anchor) . '"' : ''; ?>>
    <div class="contentWrapper">
        <div class="cols">
            <?php if ($image): ?>
                <div class="imageWrapper">
                    <img class="lazy" data-src="<?php echo esc_url($image['sizes']['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                </div>
            <?php endif; ?>
            <div class="col small"></div>
            <div class="col">
                <?php if ($title): ?>
                    <h2 class="hugeTitle" data-split-text>
                        <?php
                        $allowed_tags = '<strong><em><br><img>';
                        echo nl2br(strip_tags($title, $allowed_tags));
                        ?>
                    </h2>
                <?php endif; ?>

                <?php if ($subtitle): ?>
                    <h3 class="quoteTitle"><?php echo esc_html($subtitle); ?></h3>
                <?php endif; ?>

                <div class="cta">
                    <?php if ($cta_title): ?>
                        <h4 class="normalTitle" data-split-text>
                            <?php
                            $allowed_tags = '<strong><em><br>';
                            echo nl2br(strip_tags($cta_title, $allowed_tags));
                            ?>
                        </h4>
                    <?php endif; ?>
                    
                    <?php if ($mail): ?>
                        <a class="button secondary" href="mailto:<?php echo esc_attr($mail); ?>" title="Boek Rise & Partners">
                            <i class="icon-contact"></i>
                            <span class="innerText"><?php echo esc_html($mail); ?></span>
                        </a>
                    <?php endif; ?>
                </div>

                <?php if (have_rows('additional_cta')): ?>
                    <div class="cta">
                        <?php while (have_rows('additional_cta')): the_row(); 
                            $add_title = get_sub_field('title');
                            $link = get_sub_field('link');
                            $link_url = $link['url'] ?? '';
                            $link_title = $link['title'] ?? '';
                            $link_target = $link['target'] ?? '_self';
                        ?>
                            <?php if ($add_title): ?>
                                <h4 class="normalTitle"><?php echo esc_html($add_title); ?></h4>
                            <?php endif; ?>

                            <?php if ($link): ?>
                                <a class="button secondary" href="<?php echo esc_url($link_url); ?>" target="<?php echo esc_attr($link_target); ?>" title="<?php echo esc_attr($link_title); ?>">
                                    <i class="icon-newspaper"></i>    
                                    <span class="innerText"><?php echo esc_html($link_title); ?></span>
                                </a>
                            <?php endif; ?>
                        <?php endwhile; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>