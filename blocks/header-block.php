<?php
$size = 'full'; // (thumbnail, medium, large, full or custom size)
$video = get_field("video");
$image = get_field("image");
?>
<section class="headerBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="border"></div>
    <div class="backgroundWrapper">
        <div class="background" data-parallax data-parallax-speed="-4">
            <?php if ($video): ?>
            <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                <source src="<?php echo esc_url($video); ?>" type="video/mp4">
            </video>
            <?php elseif( $image ): ?>
            <img class="lazy" data-src="<?php echo esc_url($image["url"]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            <?php endif; ?>
        </div>
    </div>
    <span class="circle" data-parallax data-parallax-speed="3"></span>
    <div class="contentWrapper">
        <div class="innerContent" data-parallax data-parallax-speed="2">
            <h1 class="hugeTitle white" data-split-text>
                <?php
                $content = get_field("title");
                $allowed_tags = '<strong><em><br>';
                $filtered_content = strip_tags($content, $allowed_tags);
                echo nl2br($filtered_content);
                ?>
            </h1>
            <div class="buttons">
            <?php $link = get_field('button_1');
            if( $link ) {
                $link_url = $link['url'];
                $link_title = $link['title'];
                $link_target = $link['target'] ? $link['target'] : '_self';
                echo '<a class="button" href="' . esc_url( $link_url ) . '" title="'. esc_html( $link_title ) .'" target="' . esc_attr( $link_target ) . '"><i class="icon-contact"></i><span class="innerText">' . esc_html( $link_title ) . '</span></a>';
            } ?>
            <?php $link = get_field('button_2');
            if( $link ) {
                $link_url = $link['url'];
                $link_title = $link['title'];
                $link_target = $link['target'] ? $link['target'] : '_self';
                echo '<a class="button outline" href="' . esc_url( $link_url ) . '" title="'. esc_html( $link_title ) .'" target="' . esc_attr( $link_target ) . '"><i class="icon-agenda"></i><span class="innerText">' . esc_html( $link_title ) . '</span></a>';
            } ?>
        </div>
    </div>
  </div>
</section>