// out: false
.algemeenTekstRiseBlock {
    &.inview {
        .contentWrapper {
            .algemeneInfoCard, .textCard {
                opacity: 1;
                .transform(translateY(0));
            }
            .algemeneInfoCard {
                transition: opacity .9s .9s ease-in-out, transform .9s .9s cubic-bezier(0.22, 1, 0.36, 1);
            }
            .textCard {
                transition: opacity .9s 1.2s ease-in-out, transform .9s 1.2s cubic-bezier(0.22, 1, 0.36, 1);
            }
        }
    }

    .contentWrapper {
        .cols {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            .col {
                display: inline-block;
                vertical-align: top;
                &:first-child {
                    width: @vw100 * 6;
                }
                &:last-child {
                    margin-top: @vw50;
                    width: @vw100 * 2;
                }
            }
        }

        .algemeenContent {
            strong {
                font-weight: 600;
                color: @primaryColor;
            }

            em {
                font-style: italic;
            }
        }

        .algemeneInfoCard, .textCard {
            opacity: 0;
            .transform(translateY(@vw80));
        }

        .algemeneInfoCard {
            line-height: 1.7;
            background: @hardWhite;
            padding: @vw22 @vw33 @vw30 @vw33;
            font-size: @vw17;
            position: relative;
            &:before {
                content: '';
                pointer-events: none;
                position: absolute;
                top: 0;
                left: 0;
                width: 0;
                height: 0;
                .transform(translateY(calc(-100% ~"+" 1px)));
                border-top: @vw50 solid transparent;
                border-left: @vw100 * 2 solid @hardWhite;
                border-bottom: 0 solid transparent;
            }
            .tinyTitle {
                margin-bottom: @vw8;
            }
        }
        .textCard {
            position: relative;
            margin-top: @vw50;
            margin-bottom: @vw60;
            background: @hardWhite;
            padding: @vw44 @vw33 @vw30 @vw33;
            &:after {
                content: '';
                pointer-events: none;
                position: absolute;
                top: auto;
                bottom: 0;
                left: 0;
                width: 0;
                height: 0;
                .transform(translateY(calc(100% ~"-" 1px)));
                border-bottom: @vw50 solid transparent;
                border-right: calc((@vw100 * 6) + 1px) solid @hardWhite;
                border-top: 0 solid transparent;
            }
            .tinyTitle {
                margin-bottom: @vw40;
            }
        }
        .mailIcon {
            display: inline-block;
            vertical-align: middle;
            svg {
                width: @vw23;
                height: auto;
                path {
                    fill: @secondaryColor;
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .algemeenTekstRiseBlock {
        padding: @vw80-1160 0;

        .contentWrapper {
            .cols {
                gap: @vw60-1160;
            }

            .algemeenContent {
                font-size: @vw24-1160;

                p {
                    margin-bottom: @vw20-1160;
                }
            }

            .algemeneInfoCard {
                border: @vw2-1160 solid #E5E5E5;
                .rounded(@vw12-1160);
                padding: @vw40-1160;
                .box-shadow(0 @vw4-1160 @vw20-1160 rgba(0, 0, 0, 0.1));

                .bedrijfsnaam {
                    font-size: @vw28-1160;
                    margin-bottom: @vw15-1160;
                }

                .contactpersoon {
                    font-size: @vw22-1160;
                    margin-bottom: @vw25-1160;
                }

                .infoList {
                    .infoItem {
                        font-size: @vw18-1160;
                        margin-bottom: @vw12-1160;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .algemeenTekstRiseBlock {
        padding: @vw80-580 0;

        .contentWrapper {
            .cols {
                flex-direction: column;
                gap: @vw60-580;
            }

            .algemeenContent {
                font-size: @vw24-580;

                p {
                    margin-bottom: @vw20-580;
                }
            }

            .algemeneInfoCard {
                border: @vw2-580 solid #E5E5E5;
                .rounded(@vw12-580);
                padding: @vw40-580;
                .box-shadow(0 @vw4-580 @vw20-580 rgba(0, 0, 0, 0.1));

                .bedrijfsnaam {
                    font-size: @vw28-580;
                    margin-bottom: @vw15-580;
                }

                .contactpersoon {
                    font-size: @vw22-580;
                    margin-bottom: @vw25-580;
                }

                .infoList {
                    .infoItem {
                        font-size: @vw18-580;
                        margin-bottom: @vw12-580;
                    }
                }
            }
        }
    }
}
