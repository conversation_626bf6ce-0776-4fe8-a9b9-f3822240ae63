// out: false
.contactCardRiseBlock {
    padding: @vw40 0;
    
    .contentWrapper {
        .contactCard {
            background: @hardWhite;
            border: @vw2 solid #E5E5E5;
            .rounded(@vw12);
            padding: @vw30;
            max-width: @vw336;
            margin: 0 auto;
            text-align: center;
            .box-shadow(0 @vw4 @vw20 rgba(0, 0, 0, 0.1));
            
            .contactNaam {
                font-size: @vw24;
                font-weight: 600;
                color: @primaryColor;
                margin-bottom: @vw20;
                margin-top: 0;
            }
            
            .contactAdres {
                font-size: @vw18;
                line-height: 1.4;
                color: #666;
                margin-bottom: @vw16;
                
                p {
                    margin-bottom: @vw8;
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
            
            .contactTelefoon,
            .contactEmail {
                margin-bottom: @vw12;
                
                a {
                    font-size: @vw18;
                    color: @primaryColor;
                    text-decoration: none;
                    .transition(0.3s);
                    
                    &:hover {
                        color: @secondaryColor;
                        text-decoration: underline;
                    }
                }
                
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .contactCardRiseBlock {
        padding: @vw40-1160 0;
        
        .contentWrapper {
            .contactCard {
                border: @vw2-1160 solid #E5E5E5;
                .rounded(@vw12-1160);
                padding: @vw30-1160;
                max-width: @vw336-1160;
                .box-shadow(0 @vw4-1160 @vw20-1160 rgba(0, 0, 0, 0.1));
                
                .contactNaam {
                    font-size: @vw24-1160;
                    margin-bottom: @vw20-1160;
                }
                
                .contactAdres {
                    font-size: @vw18-1160;
                    margin-bottom: @vw16-1160;
                    
                    p {
                        margin-bottom: @vw8-1160;
                    }
                }
                
                .contactTelefoon,
                .contactEmail {
                    margin-bottom: @vw12-1160;
                    
                    a {
                        font-size: @vw18-1160;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .contactCardRiseBlock {
        padding: @vw40-580 0;
        
        .contentWrapper {
            .contactCard {
                border: @vw2-580 solid #E5E5E5;
                .rounded(@vw12-580);
                padding: @vw30-580;
                max-width: @vw336-580;
                .box-shadow(0 @vw4-580 @vw20-580 rgba(0, 0, 0, 0.1));
                
                .contactNaam {
                    font-size: @vw24-580;
                    margin-bottom: @vw20-580;
                }
                
                .contactAdres {
                    font-size: @vw18-580;
                    margin-bottom: @vw16-580;
                    
                    p {
                        margin-bottom: @vw8-580;
                    }
                }
                
                .contactTelefoon,
                .contactEmail {
                    margin-bottom: @vw12-580;
                    
                    a {
                        font-size: @vw18-580;
                    }
                }
            }
        }
    }
}
