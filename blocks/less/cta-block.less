// out: false
.ctaBlock {
    .cols {
        margin-left: -@vw8;
        width: calc(100% ~"+" @vw16);
        .imageWrapper {
            position: absolute;
            top: -@vw120;
            left: -@vw36; 
            width: calc(33.3333% ~"+" @vw17);
            height: calc(100% ~"+" @vw200 ~"+" @vw40);
            .border-radius(0,0, @vw20,@vw20);
            overflow: hidden;
            img, video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                object-fit: cover;
                object-position: center;
            }
        }
        .col {
            display: inline-block;
            vertical-align: top;
            margin: 0 @vw8;
            &.small {
                width: calc(33.3333% ~"-" @vw16);
            }
            &:not(.small) {
                padding-left: @vw112 + @vw16;
                width: calc(66.6666% ~"-" @vw16);
            }
            .hugeTitle {
                font-weight: 600;
                img {
                    display: inline-block;
                    width: @vw97;
                    height: @vw97;
                    object-fit: contain;
                    vertical-align: bottom;
                }
            }
            .quoteTitle {
                margin: @vw20 0;
            }
            .cta {
                display: block;
                padding-top: @vw15;
                border-top: 2px dashed rgba(255,255,255,.2);
                &:not(:last-child) {
                    padding-bottom: @vw10;
                    margin-bottom: @vw10;
                }
                .normalTitle {
                    display: inline-block;
                    margin-right: @vw20;
                    vertical-align: bottom;
                }
                .button {
                    display: inline-block;
                    vertical-align: bottom;
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .ctaBlock {
        .cols {
            margin-left: -@vw8-1160;
            width: calc(100% + @vw16-1160);
            .imageWrapper {
                top: -@vw120-1160;
                left: -@vw36-1160;
                width: calc(28% + @vw17-1160);
                height: calc(100% + @vw100-1160 + @vw100-1160 + @vw40-1160);
                .border-radius(0, 0, @vw20-1160, @vw20-1160);
            }
            .col {
                margin: 0 @vw8-1160;
                &.small {
                    width: calc(25% - @vw16-1160);
                }
                &:not(.small) {
                    padding-left: @vw40-1160;
                    width: calc(75% - @vw16-1160);
                }
                .hugeTitle {
                    img {
                        width: @vw97-1160;
                        height: @vw97-1160;
                    }
                }
                .quoteTitle {
                    margin: @vw20-1160 0;
                }
                .cta {
                    padding-top: @vw15-1160;
                    &:not(:last-child) {
                        padding-bottom: @vw10-1160;
                        margin-bottom: @vw10-1160;
                    }
                    .normalTitle {
                        margin-right: @vw20-1160;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .ctaBlock {
        .cols {
            margin-left: -@vw8-580;
            width: calc(100% + @vw16-580);
            .imageWrapper {
                display: none;
            }
            .col {
                margin: 0 @vw8-580;
                &.small {
                    display: none;
                }
                &:not(.small) {
                    padding-left: 0;
                    width: calc(100% - @vw16-580);
                }
                .hugeTitle {
                    text-align: center;
                    font-size: @vw75-580;
                    .line {
                        &:first-child {
                            font-size: @vw100-580 * 2;
                        }
                    }
                    img {
                        width: @vw100-580;
                        height: @vw100-580;
                    }
                }
                .quoteTitle {
                    margin: @vw20-580 0;
                }
                .cta {
                    padding-top: @vw15-580;
                    &:not(:last-child) {
                        padding-bottom: @vw10-580;
                        margin-bottom: @vw15-580;
                    }
                    .normalTitle {
                        margin-right: @vw20-580;
                    }
                    .button {
                        margin-top: @vw10-580;
                        display: block;
                        width: 100%;
                    }
                }
            }
        }
    }
}
