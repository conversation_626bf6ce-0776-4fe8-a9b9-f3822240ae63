// out: false
.headerBlock {
    padding-top: (@vw100 * 4) + @vw60;
    padding-bottom: @vw100 + @vw20;
    position: relative;
    overflow: hidden;
    &.inview {
        .buttons {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s .45s ease-in-out, transform .45s .45s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .backgroundWrapper {
            opacity: 1;
            transition: opacity .45s ease-in-out;
        }
    }
    .backgroundWrapper {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        width: 100%;
        height: 100%;
        &:before {
            content: '';
            background: radial-gradient(rgba(0,0,0,.4),rgba(0,0,0,0));
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .background {
            position: absolute;
            top: -10%;
            left: 0;
            width: 100%;
            height: 140%;
        }
        img, video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            object-fit: cover;
            object-position: center;
        }
    }
    .circle {
        .rounded(50%);
        background: @hardWhite;
        -webkit-mask-image: radial-gradient(rgba(0,0,0,.4), rgba(0,0,0,0), rgba(0,0,0,0));
        mask-image: radial-gradient(rgba(0,0,0,.4), rgba(0,0,0,0), rgba(0,0,0,0));
        mix-blend-mode: lighten;
        position: absolute;
        top: 50%;
        left: 50%;
        .transform(translate(-50%,-50%));
        width: (@vw112 * 6) + (@vw16 * 5);
        height: (@vw112 * 6) + (@vw16 * 5); 
    }
    .innerContent {
        padding: 0 @vw100; 
    }
    .contentWrapper {
        text-align: center;
        .innerContent {
            padding: 0 (@vw100 * 3) + @vw20;
        }
    }
    .buttons {
        opacity: 0;
        .transform(translateY(@vw20));
        margin-top: @vw22;
        .button {
            text-align: left;
            &:not(:last-child) {
                margin-right: @vw10;
            }
        }
    }
    .border {
        background: @hardWhite;
        position: absolute;
        bottom: 0;
        z-index: 1;
        width: 100%;
        .transform(translateY(50%));
        height: @vw100 + @vw20;
        .rounded(50%);
    }
}

@media all and (max-width: 1160px) { 
    .headerBlock {
        padding-top: (@vw100-1160 * 4) + @vw60-1160;
        padding-bottom: @vw100-1160 + @vw20-1160;
        .circle {
            width: (@vw112-1160 * 6) + (@vw16-1160 * 5);
            height: (@vw112-1160 * 6) + (@vw16-1160 * 5);
        }
        .contentWrapper {
            .innerContent {
                padding: 0 @vw50-1160;
            }
        }
        .buttons {
            .transform(translateY(@vw20-1160));
            margin-top: @vw22-1160;
            .button {
                &:not(:last-child) {
                    margin-right: @vw10-1160;
                }
            }
        }
        .border {
            height: @vw100-1160 + @vw20-1160;
        }
    }
}

@media all and (max-width: 580px) {
    .headerBlock {
        padding-top: (@vw100-580 * 6) + @vw60-580;
        padding-bottom: @vw100-580 + @vw20-580;
        .backgroundWrapper {
            &:before {
                background: radial-gradient(rgba(18, 63, 119,0),rgba(18, 63, 119,.4));
            }
            .background {
                z-index: -1;
            }
        }
        .circle {
            width: (@vw112-580 * 5) + (@vw16-580 * 5);
            height: (@vw112-580 * 5) + (@vw16-580 * 5);
        }
        .contentWrapper {
            .innerContent {
                padding: 0;
            }
        }
        .buttons {
            .transform(translateY(@vw20-580));
            margin-top: @vw22-580;
            .button {
                &:not(:last-child) {
                    margin-right: @vw10-580;
                }
            }
        }
        .border {
            height: @vw100-580 + @vw20-580;
        }
    }
}
