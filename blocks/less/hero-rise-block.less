// out: false
.heroRiseBlock {
    margin: 0 !important;
    overflow: hidden;
    &.inview {
        .backgroundWrapper {
            opacity: 1;
            .transitionMore(opacity, .6s, 0s, ease-in-out);
        }
        .overlay {
            opacity: 1;
            .transitionMore(opacity, .6s, .3s, ease-in-out);
        }
    }
    .backgroundWrapper {
        opacity: 0;
        position: relative;
        top: 0;
        left: 0;
        width: 100%;
        height: 0;
        .paddingRatio(1200, 460);
        z-index: 1;
        .background {
            position: absolute;
            top: -10%;
            left: 0;
            width: 100%;
            height: 110%;
            
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
            }
        }
    }
    &:has(.backgroundWrapper .background img) {
        .overlay {
            position: absolute;
            background: rgba(@primaryColor, 0.7);
            z-index: 2;
        }
    }
    .overlay {
        bottom: 0;
        left: 0;
        width: 100%;
        height: auto;
        background: @primaryColor;
        padding: @vw27 0 @vw40 0;
        opacity: 0;
    }
    
    .contentWrapper {
        position: relative;
    }
}

@media all and (max-width: 1160px) {
    .heroRiseBlock {
        min-height: @vw650-1160;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw946-1160;
                
                .hugeTitle {
                    margin-bottom: @vw40-1160;
                }
                
                .heroSubtitle {
                    font-size: @vw24-1160;
                    
                    p {
                        margin-bottom: @vw20-1160;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .heroRiseBlock {
        min-height: @vw650-580;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw946-580;
                
                .hugeTitle {
                    margin-bottom: @vw40-580;
                }
                
                .heroSubtitle {
                    font-size: @vw24-580;
                    
                    p {
                        margin-bottom: @vw20-580;
                    }
                }
            }
        }
    }
}
