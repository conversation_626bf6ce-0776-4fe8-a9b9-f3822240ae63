// out: false
.opleidingenRiseBlock {
    &:before {
        content: '';
        width: 100%;
        bottom: -@vw46;
        height: 80%;
        background: @lightGrey;
        position: absolute;
        left: 0;
        z-index: 0;
    }
    &.inview {
        .contentWrapper {
            .opleidingenCard, .imageCard {
                opacity: 1;
                .transform(translateY(0));
            }
            .imageCard {
                transition: opacity .9s .45s ease-in-out, transform .9s .45s cubic-bezier(0.22, 1, 0.36, 1);
            }
            .opleidingenCard {
                transition: opacity .9s 0s ease-in-out, transform .9s 0s cubic-bezier(0.22, 1, 0.36, 1);
                ul {
                    li {
                        opacity: 1;
                        .transform(translateY(0));
                        transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.22, 1, 0.36, 1);
                        -webkit-transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.22, 1, 0.36, 1);
                        .stagger(50, .15s);
                    }
                }
            }
        }
    }

    .quoteTitle {
        margin: @vw80 0 @vw10 0;
        text-align: right;
    }

    .contentWrapper {
        .cols {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            .col {
                display: inline-block;
                vertical-align: bottom;
                &:first-child {
                    // padding-right: @vw85;
                    width: @vw100 * 3.5;
                }
                &:last-child {
                    width: @vw100 * 4.5;
                }
            }
        }

        .opleidingenCard, .imageCard {
            opacity: 0;
            .transform(translateY(@vw80));
        }

        .imageCard {
            position: relative;
            width: 100%;
            &:after {
                content: '';
                pointer-events: none;
                position: absolute;
                top: 0;
                left: 0;
                width: 0;
                height: 0;
                .transform(translateY(-1px));
                border-bottom: @vw50 solid transparent;
                border-left: calc((@vw100 * 4.5) + 1px) solid @backgroundColor;
                border-top: 0 solid transparent;
            }
            .innerImage {
                .paddingRatio(1,1.1);
                height: 0;
                width: 100%;
                position: relative;
                img, video {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: left;
                    pointer-events: none;
                }
            }
        }

        .opleidingenCard {
            line-height: 1.7;
            background: @secondaryColor;
            padding: @vw44 @vw33 @vw30 @vw33;
            position: relative;
            width: auto;
            &:after {
                content: '';
                pointer-events: none;
                position: absolute;
                bottom: 0;
                left: 0;
                width: 0;
                height: 0;
                .transform(translateY(calc(100% ~"-" 1px)));
                border-bottom: @vw50 solid transparent;
                border-left: calc((@vw100 * 3.5) + 1px) solid @secondaryColor;
                border-top: 0 solid transparent;
            }
            .tinyTitle {
                margin-bottom: @vw8;
            }
            ul {
                list-style: none;
                padding: 0;
                margin: 0;
                line-height: 1.4;
                li {
                    font-weight: 600;
                    opacity: 0;
                    .transform(translateY(@vw10));
                }
            }
        }
    }
}
