// out: false
.quoteBlock {
    .contentWrapper {
        text-align: center;
        .floatingImage {
            display: block;
            width: (@vw112 * 2) + @vw16;
            margin: auto;
            padding-bottom: @vw50;
            position: relative;
            &:after {
                .rounded(50%);
                content: '';
                position: absolute;
                bottom: @vw20;
                mix-blend-mode: normal;
                background: @primaryColor;
                -webkit-mask-image: radial-gradient(rgba(0,0,0,.4), rgba(0,0,0,0), rgba(0,0,0,0));
                mask-image: radial-gradient(rgba(0,0,0,.4), rgba(0,0,0,0), rgba(0,0,0,0));
                width: 70%;
                margin: auto;
                height: @vw20;
                left: 50%;
                top: auto;
                animation: shadowAnimation 3s infinite ease-in-out;
            }
            img {
                width: 100%;
                height: auto;
                display: block;
                object-fit: contain;
                animation: floatAnimation 3s infinite ease-in-out;
            }
        }
        .quoteTitle {
            margin-top: @vw40;
        }
    }
}

@media all and (max-width: 1160px) {
  .quoteBlock {
      .contentWrapper {
          .floatingImage {
              width: (@vw112-1160 * 2) + @vw16-1160;
              padding-bottom: @vw50-1160;
              &:after {
                  bottom: @vw20-1160;
                  height: @vw20-1160;
              }
          }
          .quoteTitle {
              margin-top: @vw40-1160;
          }
      }
  }
}

@media all and (max-width: 580px) {
  .quoteBlock {
      .contentWrapper {
          .floatingImage {
              width: (@vw112-580 * 2) + @vw16-580;
              padding-bottom: @vw50-580;
              &:after {
                  bottom: @vw20-580;
                  height: @vw20-580;
              }
          }
          .quoteTitle {
              margin-top: @vw40-580;
          }
      }
  }
}

@keyframes floatAnimation {
    0% {
      transform: scale(1) rotate(0deg);
    }
    50% {
      transform: scale(1.05) rotate(2deg);
    }
    100% {
      transform: scale(1) rotate(0deg);
    }
  }
  
  @keyframes shadowAnimation {
    0% {
      transform: translate(-50%, -20%) scale(1);
      opacity: 0.5;
    }
    50% {
      transform: translate(-50%, -20%) scale(1.2);
      opacity: 0.7;
    }
    100% {
      transform: translate(-50%, -20%) scale(1);
      opacity: 0.5;
    }
  }