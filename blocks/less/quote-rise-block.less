// out: false
.quoteRiseBlock {
    padding: @vw80 0;
    text-align: center;
    
    .contentWrapper {
        .innerContent {
            max-width: @vw748;
            margin: 0 auto;
            
            .quoteText {
                font-family: 'DancingScript', cursive;
                font-size: @vw36;
                line-height: 1.3;
                color: @primaryColor;
                font-style: italic;
                margin: 0;
                position: relative;
                
                &:before,
                &:after {
                    content: '"';
                    font-size: @vw48;
                    color: @secondaryColor;
                    position: absolute;
                    top: -@vw10;
                }
                
                &:before {
                    left: -@vw30;
                }
                
                &:after {
                    right: -@vw30;
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .quoteRiseBlock {
        padding: @vw80-1160 0;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw748-1160;
                
                .quoteText {
                    font-size: @vw36-1160;
                    
                    &:before,
                    &:after {
                        font-size: @vw48-1160;
                        top: -@vw10-1160;
                    }
                    
                    &:before {
                        left: -@vw30-1160;
                    }
                    
                    &:after {
                        right: -@vw30-1160;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .quoteRiseBlock {
        padding: @vw80-580 0;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw748-580;
                
                .quoteText {
                    font-size: @vw36-580;
                    
                    &:before,
                    &:after {
                        font-size: @vw48-580;
                        top: -@vw10-580;
                    }
                    
                    &:before {
                        left: -@vw30-580;
                    }
                    
                    &:after {
                        right: -@vw30-580;
                    }
                }
            }
        }
    }
}
