// out: false

.sliderBlock {
  &.inview {
    .innerContent {
      .confetti {
        .slinger {
          .stagger(50, .15s);
          transition: opacity .45s ease-in-out, transform .6s cubic-bezier(0.34, 1.56, 0.64, 1);
          &.rect {
            .transform(rotate(45deg) scale(1));
          }
          &.circle {
            .transform(scale(1));
          }
        } 
      }
    }
  }
  .innerContent {
    z-index: 2;
    padding-top: @vw44;
    display: block;
    margin: auto;
    margin-bottom: -@vw50;
    width: (@vw112 * 5) + (@vw16 * 4);
    text-align: center;
    position: relative;
    .confetti {
      top: 0;
      width: @vw112 + @vw16 + @vw16;
      position: absolute;
      height: @vw100 + @vw50;
      &.left {
        left: 0;
        .transform(translateX(-100%));
        .slinger {
          &:nth-child(1) {
            left: @vw30;
            top: 0;
          }
          &:nth-child(2) {
            right: @vw22;
            top: @vw30;
          }
          &:nth-child(3) {
            bottom: @vw10;
            left: 0;
          }
          &:nth-child(4) {
            bottom: @vw20;
            right: 0;
          }
        }
      }
      &.right {
        right: 0;
        .transform(translateX(100%));
        .slinger {
          &:nth-child(1) {
            left: 0;
            top: 0;
          }
          &:nth-child(2) {
            top: @vw60;
            right: @vw30;
          }
          &:nth-child(3) {
            bottom: 0;
            left: @vw20;
          }
        }
      }
      .slinger {
        position: absolute;
        &.rect{
          background: @primaryColor;
          height: @vw27; 
          width: @vw27;
          .transform(rotate(0deg) scale(0));
        }
        &.circle{
          .transform(scale(0));
          .rounded(50%);
          background: @secondaryColor;
          height: @vw31;
          width: @vw31;
          &.big {
            height: @vw55;
            width: @vw55;
          }
        }
      }
    }
  }
  .contentWrapper {
    position: relative;
  }
  .sliderWrapper {
    position: relative;
  }
  .slider {
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .slide {
      display: inline-block;
      margin: 0 @vw8;
      width: calc(33.3333% ~"-" @vw16);
      .imageWrapper {
        margin-bottom: @vw20;
        width: 100%;
        position: relative;
        overflow: hidden;
        .rounded(@vw20);
        height: auto;
        .innerImage {
          .paddingRatio(409,537);
          height: 0;
          width: 100%;
          position: relative;
          img, video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            pointer-events: none;
          }
        }
      }
    }
  }
  .sliderButton {
    position: absolute;
    cursor: pointer;
    width: @vw70;
    height: @vw70;
    background: @primaryColor;
    color: @hardWhite;
    text-align: center;
    overflow: hidden;
    line-height: @vw74;
    .rounded(50%); 
    top: 50%;
    transition: color .3s ease-in-out, background .3s ease-in-out;
    -webkit-transition: color .3s ease-in-out, background .3s ease-in-out;
    &.disabled {
      pointer-events: none;
      opacity: .4;
    }
    &:hover {
      background: @hardWhite;
      color: @primaryColor;
    }
    &.next {
      right: 0;
      transform: translateY(-50%) translateX(50%);
    }
    &.prev {
      left: 0;
      transform: translateY(-50%) translateX(-50%);
    }
    i {
      cursor: pointer;
    }
  }
  .sliderIndicator {
    margin-top: @vw50;
    width: 100%;
    overflow: hidden;
    height: 2px;
    position: relative;
    background: rgba(255,255,255,.8);
    .innerBar {
      background: @primaryColor;
      border-radius: @vw10;
      height: 100%;
      width: 0;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}

@media all and (max-width: 1160px) {
  .sliderBlock {
    .innerContent {
      .text {
        margin: @vw60-1160 0; 
      }
      .confetti {
        width: @vw112-1160 + @vw16-1160 + @vw16-1160;
        height: @vw100-1160 + @vw50-1160;
        &.left, &.right {
          .slinger {
            &.rect{
              height: @vw27-1160; 
              width: @vw27-1160;
            }
            &.circle{
              height: @vw31-1160;
              width: @vw31-1160;
              &.big {
                height: @vw55-1160;
                width: @vw55-1160;
              }
            }
            &:nth-child(1) { left: @vw30; }
            &:nth-child(2) { right: @vw22-1160; }
            &:nth-child(3) { bottom: @vw10-1160; }
            &:nth-child(4) { bottom: @vw20-1160; }
          }
        }
      }
    }
    .slider {
      margin-left: -@vw8-1160;
      width: calc(100% + @vw16-1160);
      .slide {
        margin: 0 @vw8-1160;
        width: calc(33.3333% - @vw16-1160);
        .imageWrapper {
          margin-bottom: @vw20-1160;
          .rounded(@vw20-1160);
        }
      }
    }
    .sliderButton {
      width: @vw70-1160;
      height: @vw70-1160;
      line-height: @vw70-1160;
    }
    .sliderIndicator {
      margin-top: @vw50-1160;
      .innerBar {
        border-radius: @vw10-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .sliderBlock {
    .innerContent {
      .text {
        margin: @vw60-580 0;
      }
      .confetti {
        width: @vw112-580 + @vw16-580 + @vw16-580;
        height: @vw100-580 + @vw50-580;
        &.left, &.right {
          .slinger {
            &.rect{
              height: @vw20-580; 
              width: @vw20-580;
            }
            &.circle{
              height: @vw25-580;
              width: @vw25-580;
              &.big {
                height: @vw40-580;
                width: @vw40-580;
              }
            }
            &:nth-child(1) { left: @vw30-580; }
            &:nth-child(2) { right: @vw22-580; }
            &:nth-child(3) { bottom: @vw10-580; }
            &:nth-child(4) { bottom: @vw20-580; left: @vw60-580; }
          }
        }
        &.right {
          .transform(rotate(180deg) translateX(-100%));
        }
      }
    }
    .slider {
      margin-left: -@vw8-580;
      width: calc(100% + @vw16-580);
      .slide {
        margin: 0 @vw8-580;
        width: calc(100% - @vw16-580);
        .imageWrapper {
          margin-bottom: @vw20-580;
          .rounded(@vw20-580);
          .innerImage {
            .paddingRatio(9,14);
          }
        }
      }
    }
    .sliderButton {
      width: @vw100-580;
      height: @vw100-580;
      line-height: @vw100-580 + @vw12-580;
    }
    .sliderIndicator {
      margin-top: @vw50-580;
      .innerBar {
        border-radius: @vw10-580;
      }
    }
  }
}
