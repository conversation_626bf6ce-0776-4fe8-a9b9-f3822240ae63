// out: false
.werkzaamhedenRiseBlock {
    &:before {
        content: '';
        width: 100%;
        bottom: -@vw46;
        height: 100%;
        background: @lightGrey;
        position: absolute;
        left: 0;
        z-index: 0;
    }
    &.inview {
        .contentWrapper {
            .werkzaamhedenCard, .textCard {
                opacity: 1;
                .transform(translateY(0));
            }
            .werkzaamhedenCard {
                transition: opacity .9s .45s ease-in-out, transform .9s .45s cubic-bezier(0.22, 1, 0.36, 1);
                ul {
                    li {
                        opacity: 1;
                        .transform(translateY(0));
                        transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.22, 1, 0.36, 1);
                        -webkit-transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.22, 1, 0.36, 1);
                        .stagger(50, .15s);
                    }
                }
            }
        }
    }

    .contentWrapper {
        .cols {
            .col {
                display: inline-block;
                vertical-align: bottom;
                &:first-child {
                    padding-right: @vw85;
                    width: calc(100% ~"-" @vw100 * 5);
                }
                &:last-child {
                    width: @vw100 * 5;
                }
            }
        }

        .werkzaamhedenCard {
            opacity: 0;
            .transform(translateY(@vw80));
        }

        .werkzaamhedenCard {
            line-height: 1.7;
            background: @secondaryColor;
            padding: @vw44 @vw33 @vw30 @vw33;
            position: relative;
            width: auto;
            &:before {
                content: '';
                pointer-events: none;
                position: absolute;
                top: 0;
                left: 0;
                width: 0;
                height: 0;
                .transform(translateY(calc(-100% ~"+" 1px)));
                border-top: @vw50 solid transparent;
                border-right: calc((@vw100 * 5) + 1px) solid @secondaryColor;
                border-bottom: 0 solid transparent;
            }
            .tinyTitle {
                margin-bottom: @vw8;
                padding-right: 40%;
            }
            ul {
                list-style: none;
                padding: 0;
                margin: 0;
                line-height: 1.4;
                li {
                    opacity: 0;
                    .transform(translateY(@vw10));
                    &:before {
                        content: '• ';
                        margin-right: @vw8;
                        width: @vw8;
                        display: inline-block;
                        vertical-align: top;
                        .rounded(50%);
                    }
                    span {
                        display: inline-block;
                        vertical-align: top;
                        width: calc(100% ~"-" @vw22);
                    }
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .algemeenTekstRiseBlock {
        padding: @vw80-1160 0;

        .contentWrapper {
            .cols {
                gap: @vw60-1160;
            }

            .algemeenContent {
                font-size: @vw24-1160;

                p {
                    margin-bottom: @vw20-1160;
                }
            }

            .algemeneInfoCard {
                border: @vw2-1160 solid #E5E5E5;
                .rounded(@vw12-1160);
                padding: @vw40-1160;
                .box-shadow(0 @vw4-1160 @vw20-1160 rgba(0, 0, 0, 0.1));

                .bedrijfsnaam {
                    font-size: @vw28-1160;
                    margin-bottom: @vw15-1160;
                }

                .contactpersoon {
                    font-size: @vw22-1160;
                    margin-bottom: @vw25-1160;
                }

                .infoList {
                    .infoItem {
                        font-size: @vw18-1160;
                        margin-bottom: @vw12-1160;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .algemeenTekstRiseBlock {
        padding: @vw80-580 0;

        .contentWrapper {
            .cols {
                flex-direction: column;
                gap: @vw60-580;
            }

            .algemeenContent {
                font-size: @vw24-580;

                p {
                    margin-bottom: @vw20-580;
                }
            }

            .algemeneInfoCard {
                border: @vw2-580 solid #E5E5E5;
                .rounded(@vw12-580);
                padding: @vw40-580;
                .box-shadow(0 @vw4-580 @vw20-580 rgba(0, 0, 0, 0.1));

                .bedrijfsnaam {
                    font-size: @vw28-580;
                    margin-bottom: @vw15-580;
                }

                .contactpersoon {
                    font-size: @vw22-580;
                    margin-bottom: @vw25-580;
                }

                .infoList {
                    .infoItem {
                        font-size: @vw18-580;
                        margin-bottom: @vw12-580;
                    }
                }
            }
        }
    }
}
