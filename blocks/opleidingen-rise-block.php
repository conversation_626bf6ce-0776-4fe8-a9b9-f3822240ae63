<?php
$title = get_field("title");
$opleidingen = get_field("opleidingen");
$quote = get_field("text");
$image = get_field("image");
?>
<section class="opleidingenRiseBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="cols">
            <div class="col">
                <div class="opleidingenCard">
                    <?php if( $title ): ?>
                    <h2 class="tinyTitle white">
                        <?php
                        $allowed_tags = '<strong><em><br>';
                        $filtered_content = strip_tags($title, $allowed_tags);
                        echo nl2br($filtered_content);
                        ?>
                    </h2>
                    <?php endif; ?>
                    
                    <?php if( have_rows('opleidingen') ): ?>
                    <ul>
                        <?php while( have_rows('opleidingen') ): the_row(); ?>
                            <li class="opleidingen">
                                <?php the_sub_field('item'); ?>
                            </li>
                        <?php endwhile; ?>
                    </ul>
                    <?php endif; ?>
                </div>
                <h3 class="quoteTitle" data-lines data-words>
                    <?php echo esc_html($quote); ?>
                </h3>
            </div>
            <div class="col">
                <div class="imageCard">
                    <div class="innerImage">
                        <?php if( $image ): ?>
                        <img class="lazy" data-src="<?php echo esc_url($image["url"]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
