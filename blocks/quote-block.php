<?php
$size = 'medium'; // (thumbnail, medium, large, full or custom size)
$image = get_field("image");
?>
<section class="quoteBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper small">
        <div class="floatingImage">
            <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
        </div>
        <div class="innerContent">
            <h2 class="normalTitle" data-split-text>
                <?php
                $content = get_field("title");
                $allowed_tags = '<strong><em><br>';
                $filtered_content = strip_tags($content, $allowed_tags);
                echo nl2br($filtered_content);
                ?>
            </h2>
            <h3 class="quoteTitle">
                <?php the_field("subtitle") ?>
            </h3>
        </div>
    </div>
  </div>
</section>