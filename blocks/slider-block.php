<?php
$size = 'large'; // Image size for WordPress
?>
<section class="sliderBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper small">
        <div class="innerContent">
            <span class="confetti left" data-parallax data-parallax-speed="1.4">
                <span class="slinger rect"></span>
                <span class="slinger circle big"></span>
                <span class="slinger circle small"></span>
                <span class="slinger rect"></span>
            </span>
            <h2 class="hugeTitle" data-split-text>
                <?php
                $content = get_field("title");
                $allowed_tags = '<strong><em><br>';
                $filtered_content = strip_tags($content, $allowed_tags);
                echo nl2br($filtered_content);
                ?>
            </h2>
            <span class="confetti right" data-parallax data-parallax-speed="0.5">
                <span class="slinger circle big"></span>
                <span class="slinger rect"></span>
                <span class="slinger circle small"></span>
            </span>
        </div>
    </div>
    <div class="contentWrapper">
        <div class="sliderWrapper">
        <div class="slider" data-slider>
            <?php if( have_rows('slide') ): // Check if repeater has rows ?>
            <?php while( have_rows('slide') ): the_row(); 
                $image = get_sub_field('image');
                $video = get_sub_field('video');
            ?>
                <div class="slide">
                <div class="imageWrapper">
                    <div class="innerImage">
                    <?php if ($video): ?>
                    <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                        <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                    </video>
                    <?php elseif ($image): ?>
                        <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
                    <?php endif; ?>
                    </div>
                </div>
                </div>
            <?php endwhile; ?>
            <?php else: ?>
            <p>Er zijn momenteel geen slides beschikbaar.</p>
            <?php endif; ?>
        </div>
        <div class="sliderButton prev" data-prev><i class="icon-arrow-left"></i></div>
        <div class="sliderButton next" data-next><i class="icon-arrow-right"></i></div>
        <div class="sliderIndicator"><div class="innerBar"></div></div>
        </div>
    </div>
</section>
