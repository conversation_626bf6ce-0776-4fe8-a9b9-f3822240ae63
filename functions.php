<?php

function jn_enqueue_assets() {
    $scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'FLICKITY' => '/libs/flickity.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'Split text' => '/libs/SplitText.min.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Hammer_js' => '/libs/hammer.min.js',
        'main_js' => '/assets/js/main.js',
        'Header' => '/assets/js/header.js',
        'Parallax' => '/assets/js/parts/parallax.js',
        'Slider' => '/assets/js/parts/slider.js',
        'Inview' => '/assets/js/parts/inview.js',
        'Split' => '/assets/js/parts/split.js',
    );

    foreach ($scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    $blocks = array(
        'HEADER BLOCK' => '/blocks/js/header-block.js',
        // Tour block removed for RISE & Partners theme
    );

    foreach ($blocks as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    wp_enqueue_style('main', get_stylesheet_uri());
    // wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add favicon
function ilc_favicon() {
    echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
    $sections = array(
        'customTheme-main-callout-title' => 'Title',
        'customTheme-main-callout-description' => 'Description',
        'customTheme-main-callout-featured-image' => 'Image',
        'customTheme-main-callout-logo' => 'Logo',
        'customTheme-main-callout-logo-white' => 'Logo (White)',
        'customTheme-main-callout-contactpersoon' => 'Contact Person',
        'customTheme-main-callout-telephone' => 'Telephone',
        'customTheme-main-callout-telephone-label' => 'Telephone label',
        'customTheme-main-callout-mail' => 'Mail',
        'customTheme-main-callout-kvk' => 'KvK',
        'customTheme-main-callout-address' => 'Address',
        'customTheme-main-callout-zipcode' => 'Zip Code',
        'customTheme-main-callout-facebook' => 'Facebook URL',
        'customTheme-main-callout-linkedin' => 'LinkedIn URL',
        'customTheme-main-callout-instagram' => 'Instagram URL',
        'customTheme-main-callout-spotify' => 'Spotify URL',
        'customTheme-main-callout-youtube' => 'Youtube URL',
        'customTheme-main-callout-analytics' => 'Analytics ID',
    );

    $wp_customize->add_section('customTheme-main-callout-section', array(
        'title' => 'Main Information'
    ));

    foreach ($sections as $setting_id => $label) {
        $wp_customize->add_setting($setting_id);
        $control_args = array(
            'label' => $label,
            'section' => 'customTheme-main-callout-section',
            'settings' => $setting_id
        );

        if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false) {
            $control_args['width'] = 750;
            $control_args['height'] = 500;
            $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
        } elseif ($label === 'Description') {
            $control_args['type'] = 'textarea';
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        } else {
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        }
    }
}

add_action('customize_register', 'jn_customize_register');

// Register menus
function jn_register_menus() {
    register_nav_menus(array(
        'primary-menu' => 'Primary Menu'
    ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
    unset($robots['max-image-preview']);
    return $robots;
}

// blocks
add_action('acf/init', 'my_acf_blocks_init');
function my_acf_blocks_init() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

        // Register a testimonial block.
        acf_register_block_type(array(
            'name'              => 'header_block',
            'title'             => __('Header Block'),
            'render_template'   => 'blocks/header-block.php',
            'category'          => 'header',
        ));
        acf_register_block_type(array(
            'name'              => 'quote_block',
            'title'             => __('Quote Block'),
            'render_template'   => 'blocks/quote-block.php',
            'category'          => 'quote',
        ));
        // Tour block removed for RISE & Partners theme
        acf_register_block_type(array(
            'name'              => 'slider_block',
            'title'             => __('Slider Block'),
            'render_template'   => 'blocks/slider-block.php',
            'category'          => 'slider',
        ));
        acf_register_block_type(array(
            'name'              => 'cta_block',
            'title'             => __('CTA Block'),
            'render_template'   => 'blocks/cta-block.php',
            'category'          => 'cta',
        ));

        // RISE & Partners blocks
        acf_register_block_type(array(
            'name'              => 'hero_rise_block',
            'title'             => __('Hero RISE Block'),
            'render_template'   => 'blocks/hero-rise-block.php',
            'category'          => 'rise',
        ));
        acf_register_block_type(array(
            'name'              => 'intro_rise_block',
            'title'             => __('Intro RISE Block'),
            'render_template'   => 'blocks/intro-rise-block.php',
            'category'          => 'rise',
        ));
        acf_register_block_type(array(
            'name'              => 'werkzaamheden_rise_block',
            'title'             => __('Werkzaamheden RISE Block'),
            'render_template'   => 'blocks/werkzaamheden-rise-block.php',
            'category'          => 'rise',
        ));
        acf_register_block_type(array(
            'name'              => 'quote_rise_block',
            'title'             => __('Quote RISE Block'),
            'render_template'   => 'blocks/quote-rise-block.php',
            'category'          => 'rise',
        ));
        acf_register_block_type(array(
            'name'              => 'contact_card_rise_block',
            'title'             => __('Contact Card RISE Block'),
            'render_template'   => 'blocks/contact-card-rise-block.php',
            'category'          => 'rise',
        ));
        acf_register_block_type(array(
            'name'              => 'opleidingen_rise_block',
            'title'             => __('Opleidingen RISE Block'),
            'render_template'   => 'blocks/opleidingen-rise-block.php',
            'category'          => 'rise',
        ));
        acf_register_block_type(array(
            'name'              => 'algemeen_tekst_rise_block',
            'title'             => __('Algemeen Tekst RISE Block'),
            'render_template'   => 'blocks/algemeen-tekst-rise-block.php',
            'category'          => 'rise',
        ));
        acf_register_block_type(array(
            'name'              => 'algemene_info_rise_block',
            'title'             => __('Algemene Info RISE Block'),
            'render_template'   => 'blocks/algemene-info-rise-block.php',
            'category'          => 'rise',
        ));
    }
}

// Google Calendar/API code removed for RISE & Partners theme transformation

// ACF JSON save and load paths
add_filter('acf/settings/save_json', 'my_acf_json_save_point');
function my_acf_json_save_point( $path ) {
    $path = get_stylesheet_directory() . '/acf-json';
    return $path;
}

add_filter('acf/settings/load_json', 'my_acf_json_load_point');
function my_acf_json_load_point( $paths ) {
    unset($paths[0]);
    $paths[] = get_stylesheet_directory() . '/acf-json';
    return $paths;
}



?>
