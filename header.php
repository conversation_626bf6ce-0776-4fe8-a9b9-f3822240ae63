<!DOCTYPE html>
<html lang="nl" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>
    <?php echo get_theme_mod('customTheme-main-callout-title') ?: get_bloginfo('name'); ?> |
    <?php the_title(); ?>
  </title>
  <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
  <meta name="msapplication-TileColor" content="#00aba9">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/">

  <!-- Open Graph Metadata -->
  <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ?: get_bloginfo('name'); ?> | <?php the_title(); ?>">
  <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>">
  <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>">
  <meta property="og:type" content="website">
  <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>">
  <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ?: get_bloginfo('name'); ?>">
  <meta property="og:locale" content="nl">

  <!-- Favicon and Icons -->
  <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="shortcut icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <meta name="apple-mobile-web-app-title" content="Rise & Partners">
  <link rel="manifest" href="/site.webmanifest">

  <!-- JSON-LD Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<?php echo get_bloginfo('name'); ?>",
    "url": "<?php echo esc_url(home_url()); ?>",
    "logo": "<?php echo get_stylesheet_directory_uri(); ?>/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
      "email": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?>",
      "contactOption": "Toll-free",
      "areaServed": "NL",
      "availableLanguage": "nl",
      "contactType": "customer service"
    }
  }
  </script>

  <?php wp_head(); ?>
</head>
<body class="no-scroll">
<header>
  <div class="contentWrapper">
    <div class="col">
      <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title'); ?>" class="logo">
          <span class="upperLogo">
            <svg viewBox="0 0 82.94 53.91">
              <defs>
                <style>
                  header .cls-1 {
                    fill: #72c7e9;
                  }

                  header .cls-2 {
                    fill: #57cbeb;
                  }

                  header .cls-3 {
                    fill: #004165;
                  }

                  header .cls-4 {
                    fill: #0063bd;
                  }
                </style>
              </defs>
              <path class="cls-3" d="M58.11,21.61l.51.24-.04-9.84L38.12.2l.04,12.11c6.66,3.09,13.31,6.19,19.95,9.3"/>
              <path class="cls-1" d="M38.16,13.83l.06,18.65c6.61,2.23,13.22,4.47,19.82,6.73l.65.22-.06-16.13-1.08-.5c-6.47-2.98-12.93-5.98-19.39-8.98"/>
              <path class="cls-4" d="M58.7,40.83l-1.08-.36c-6.47-2.17-12.93-4.34-19.39-6.53l.06,19.97h20.46l-.05-13.08Z"/>
              <path class="cls-1" d="M37.09,12.1l.3-.15h.01s0-11.95,0-11.95l-14.38,9.99.03,9.22c4.68-2.38,9.36-4.74,14.04-7.11"/>
              <path class="cls-3" d="M37.4,33.67c-4.76,1.74-9.52,3.48-14.28,5.21l.05,15.03h14.22v-20.24Z"/>
              <path class="cls-4" d="M8.94,42.74l4.7-1.75,4.7-1.75c1.4-.52,2.8-1.04,4.19-1.56v-6.65l-14.46,6.46.02,5.56.84-.32Z"/>
              <path class="cls-2" d="M82.94,48.87c-1.62-.53-3.23-1.06-4.85-1.6l-5.12-1.69-3.8-1.26v9.59h13.74l.02-5.04Z"/>
              <polygon class="cls-1" points="7.36 44.57 4.63 45.56 0 47.22 0 53.91 7.36 53.91 7.36 44.57"/>
              <polygon class="cls-3" points="68.08 27.66 67.79 27.52 60.01 23.94 60.01 39.89 68.06 42.65 68.08 27.66"/>
            </svg>
          </span>
          <span class="lowerLogo">
            <svg viewBox="0 0 83.14 43.14">
              <defs>
                <style>
                  header .cls-1 {
                    fill: #72c7e9;
                  }

                  header .cls-2 {
                    fill: #004165;
                  }
                </style>
              </defs>
              <path class="cls-2" d="M.07,25.7l1.42-.13c.73-.09,1.25-.35,1.25-1.21V4.31l-2.74-.13V.52h12.06c6.34,0,10.35,2.07,10.35,7.93,0,4.31-2.8,6.64-5.17,7.59,1.16.43,1.81,1.16,2.46,2.63l3.1,6.77,2.46.09v3.67h-10.65v-3.49l1.25-.13c.99-.13,1.21-.52.82-1.38l-2.03-4.31c-.6-1.34-1.21-1.98-2.72-1.98h-3.92v7.5l2.8.13v3.67H.07v-3.49ZM11.75,13.8c3.67,0,5.3-1.68,5.3-4.74,0-3.88-2.24-4.36-5.91-4.36h-3.15v9.1h3.75Z"/>
              <path class="cls-2" d="M29.73,4.31l-2.46-.13V.52h10.43v3.49l-1.42.13c-.73.09-1.25.34-1.25,1.38v19.88l2.46.13v3.67h-10.43v-3.49l1.42-.13c.82-.09,1.25-.35,1.25-1.42V4.31Z"/>
              <path class="cls-2" d="M54.48,21.17c0-2.72-2.46-3.49-5.48-4.31-4.27-1.16-8.24-2.89-8.24-8.41S44.82,0,50.77,0c2.85,0,5.52.56,8.11,1.77v6.47l-4.4.3v-2.5c0-.82-.13-1.16-.91-1.42-.82-.26-1.94-.34-2.8-.34-2.54,0-4.53,1.04-4.53,3.71s2.5,3.41,5.73,4.4c3.92,1.25,8.06,3.02,8.06,8.58s-4.48,8.8-10.43,8.8c-3.45,0-6.12-.56-8.71-1.77v-6.81l4.4-.3v2.63c0,.82.13,1.16.91,1.42.82.26,1.94.39,3.1.39,2.85,0,5.17-.91,5.17-4.14"/>
              <path class="cls-2" d="M62.89,29.19v-3.49l1.42-.13c.73-.09,1.25-.35,1.25-1.21V4.31l-2.46-.13V.52h20.03v7.16l-4.68.3v-1.72c0-.82-.09-1.34-1.25-1.34h-6.38v7.37h9.96v4.4h-9.96v8.06h6.94c1.12,0,1.21-.52,1.21-1.38v-2.16l4.11.3v7.67h-20.2Z"/>
              <path class="cls-1" d="M34.26,40.82h-.81l-.87-1.01c-.59.67-1.38,1.13-2.4,1.13-1.63,0-2.48-1.19-2.48-2.38,0-.91.52-1.75,1.75-2.43-.46-.57-.82-1.16-.82-1.87,0-.99.68-1.67,1.69-1.67,1.13,0,1.68.78,1.68,1.69s-.62,1.42-1.59,2l2.16,2.52c.29-.48.48-.97.58-1.32h.62c-.13.53-.4,1.2-.8,1.81l1.29,1.53ZM32.22,39.36l-2.38-2.8c-1.21.67-1.52,1.34-1.52,2.05s.57,1.7,1.9,1.7c.89,0,1.53-.4,2-.95M29.23,34.26c0,.49.34,1.02.8,1.57,1.06-.57,1.37-1.01,1.37-1.49,0-.66-.35-1.16-1.06-1.16s-1.1.54-1.1,1.09"/>
              <path class="cls-1" d="M44.67,37.81c0,2.56-1.49,3.11-2.81,3.11-.62,0-1.21-.11-1.75-.3v2.53h-.62v-6.67c0-.47.22-1.91,2.45-1.91,1.04,0,2.72.43,2.72,3.24M44.04,37.79c0-2.34-1.21-2.64-2.09-2.64-1.37,0-1.83.67-1.83,1.3v3.53c.52.23,1.13.34,1.75.34,1.35,0,2.18-.63,2.18-2.53"/>
              <path class="cls-1" d="M51.09,37.15v1.87c0,1.67-1.71,1.89-2.48,1.89s-2.56-.25-2.56-1.91c0-1.91,2.37-1.99,2.94-1.99.82,0,1.26.09,1.47.13,0-1.29-.63-1.94-2.02-1.94-.8,0-1.45.19-1.75.28v-.61c.46-.15,1.09-.29,1.78-.29,1.82,0,2.62.9,2.62,2.57M50.47,37.7c-.16-.03-.72-.11-1.56-.11-.77,0-2.24.18-2.24,1.4,0,1.13,1.18,1.34,1.94,1.34s1.86-.2,1.86-1.32v-1.32Z"/>
              <path class="cls-1" d="M53.46,35.57v5.25h-.62v-5.65c.35-.15,1.48-.57,2.88-.58v.61c-.96.03-1.78.24-2.26.38"/>
              <path class="cls-1" d="M60.07,40.2v.59c-.24.06-.52.11-.82.11-.47,0-1.61-.05-1.61-1.58v-4.09h-.99v-.56h.99v-1.73h.63v1.73h1.77v.56h-1.77v4.06c0,.83.48,1.02,1.05,1.02.29,0,.53-.05.75-.13"/>
              <path class="cls-1" d="M66.43,36.82v4h-.63v-4c0-.9-.38-1.62-1.81-1.62-.75,0-1.43.19-1.95.39v5.22h-.62v-5.59c.43-.23,1.44-.66,2.63-.66,1.51,0,2.38.76,2.38,2.25"/>
              <path class="cls-1" d="M73.45,37.78c0,.28-.01.32-.01.33h-4.72c.01.22.05.39.11.54.18,1.19,1.01,1.64,2.2,1.64.75,0,1.49-.15,2-.43v.65c-.57.27-1.35.39-2.05.39-1.39,0-2.91-.59-2.91-3.04,0-.97.3-3.29,2.73-3.29,2.62,0,2.64,2.8,2.64,3.2M68.72,37.55h4.09c0-.3-.08-2.38-2.04-2.38-1.33,0-1.99.89-2.05,2.38"/>
              <path class="cls-1" d="M75.64,35.57v5.25h-.62v-5.65c.35-.15,1.48-.57,2.88-.58v.61c-.96.03-1.78.24-2.26.38"/>
              <path class="cls-1" d="M83,39.22c0,.99-.76,1.69-2.23,1.69-.81,0-1.44-.2-1.92-.44v-.66c.56.29,1.2.48,1.9.48.94,0,1.62-.35,1.62-1.08,0-1.83-3.48-.78-3.48-2.95,0-.87.59-1.71,2.09-1.71.66,0,1.21.18,1.66.37v.62c-.39-.19-.94-.38-1.59-.38-.81,0-1.52.3-1.52,1.1,0,1.49,3.48.57,3.48,2.95"/>
            </svg>
          </span>
      </a>
      <div class="anchorMenu"></div>
    </div>
    <div class="col">
        <?php wp_nav_menu(array('theme_location' => 'primary-menu')); ?>
    </div>
  </div>
</header>
<div id="pageContainer" class="transition-fade blocks">